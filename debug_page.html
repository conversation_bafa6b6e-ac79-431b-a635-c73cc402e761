<!doctype html><html lang="en"><head><script data-ad-client="ca-pub-8815626444656343" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script><title>Reports - Smart Hospital : Hospital Management System by QDOCS</title><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" /><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="author" content="QDOCS"><meta name="keywords" content="Reports"/><meta name="description" content="Reports"/><meta name="theme-color" content="#424242" /><link rel="icon" type="image/png" href="https://smart-hospital.in/assets/admin/assets/img/s-favican.png"><link rel="stylesheet" type="text/css" href="https://smart-hospital.in/assets/admin/assets/css/material-design.css"><link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700" /><link rel="stylesheet" href="https://smart-hospital.in/assets/admin/assets/css/font-awesome.min.css" /><!-- <link rel="stylesheet" type="text/css" href="/css/all.css" /> --><link rel="stylesheet" type="text/css" href="https://smart-hospital.in/assets/admin/assets/css/material-design.css" /><link href="https://smart-hospital.in/assets/admin/assets/css/bootstrap.min.css" rel="stylesheet" /><link href="https://smart-hospital.in/assets/admin/assets/css/style.css" rel="stylesheet"/><link rel="stylesheet" href="https://smart-hospital.in/assets/admin/assets/css/animate.css"><link href="https://smart-hospital.in/assets/admin/assets/css/demo.css" rel="stylesheet" /><link rel="stylesheet" href="https://smart-hospital.in/assets/admin/assets/css/bootstrap-select.min.css"><link rel="stylesheet" href="https://smart-hospital.in/assets/admin/assets/css/owl.carousel.css"><script>
            var base_url = "https://smart-hospital.in/";
        </script><script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8815626444656343"
     crossorigin="anonymous"></script></head><body id="Home" class="about-us" data-spy="scroll"><section class="topbar"><div class="container"><div class="row"><div class="col-lg-6 col-md-6 col-sm-6"><ul class="social-network header-banner fleft"><li><a class="" href="#" target="_blank"><i class="fa fa-facebook"></i></a></li><li><a class="" href="#" target="_blank"><i class="fa fa-twitter"></i></a></li><li><a class="" href="#" target="_blank"><i class="fa fa-youtube"></i></a></li></ul></div><div class="col-lg-6 col-md-6 col-sm-6"><div class="topright"><a href="https://1.envato.market/smart_hospital" target="_blank" class="btn btn-success btn-sm btn-round" style="margin-top: 6px;margin-bottom: 0; font-size: 13px;"><i class="material-icons">shopping_cart</i> Buy Now $59</a></div></div></div></div></section><!-- Navbar --><nav class="navbar navbar-transparent navbar-color-on-scroll" role="navigation"><div class="container"><div class="navbar-header"><button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navigation-bar"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="https://smart-hospital.in/"><div class="logo-container"><div class="logo"><img src="https://smart-hospital.in/assets/admin/assets/img/logo.png" alt="Smart Hospital"></div></div><!--./logo-container--></a></div><!--./navbar-header--><div class="collapse navbar-collapse" id="navigation-bar"><ul class="nav navbar-nav navbar-right"><li class="" ><a href="https://smart-hospital.in/"><i class="fa fa-home"></i> Home</a></li><!-- <li class="dropdown "><a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="material-icons">shopping_cart</i> Product</a><ul class="dropdown-menu dropdown-with-icons"><li class="" ><a href=""><img src="" /> Smart Hospital</a></li><li class="" ><a href=""><img src="" /> Andorid App</a></li><li class="" ><a href=""><img src="" /> Biometric Attendance App<br /><small style="padding-left:28px">(Coming soon)</small></a></li></ul></li>--><li class="" ><a href="https://smart-hospital.in/feature"><i class="fa fa-server fasize2"></i> Features</a></li><li class="" ><a href="https://smart-hospital.in/demo"><i class="material-icons">computer</i> Demo</a></li><li class="" ><a href="https://smart-hospital.in/how-it-work"><i class="fa fa-lightbulb-o"></i> How it Works</a></li><li class="dropdown active"><a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="material-icons">description</i> Help & Documentation</a><ul class="dropdown-menu dropdown-with-icons"><li class="" ><a href="https://smart-hospital.in/articles"><i class="fa fa-book fasize2"></i> Articles</a></li><li class="" ><a href="https://smart-hospital.in/faq"><i class="fa fa-question-circle fasize2"></i> FAQ</a></li><li><a href="https://smart-hospital.in/docs" target="_blank"><i class="material-icons fasize2">assignment</i>User Documentation</a></li><!--<li><a href="https://www.youtube.com/playlist?list=PLRyqKhLrbTa04IOguu-mjojT5sFbInUQa" target="_blank"><i class="fa fa-play-circle fasize2"></i> Video Tutorial</a></li> --><li class=""><a href="https://smart-hospital.in/category/changelog"><i class="material-icons fasize2">code</i> Changelog</a></li><li><a href="https://smart-hospital.in/category/known-issues"><i class="material-icons fasize2">bug_report</i> Known Issues</a></li></ul></li><li><a href="https://support.qdocs.net" target="_blank"><i class="fa fa-life-ring fasize2"></i> Support</a></li><!--<li class="" ><a href=""><i class="fa fa-book fasize2"></i> Articles</a></li><li class="" ><a href=""><i class="fa fa-question-circle fasize2"></i> FAQ</a></li> --></ul></div></div></nav><!--End Navbar--><div class="wrapper"><div class="page-header header-filter innerbanner" data-parallax="active" ><div class="container"><div class="row"><div class="col-md-10 col-md-offset-1"><div class="topsearch"><form id="new_article_form" method="POST" autocomplete="off" action="https://smart-hospital.in/home/<USER>" class="article_form"><div class="autocomplete"><input id="myInput" class="subsearchinput" type="text" name="keyword" placeholder="Enter your search term here..."><button type="submit" class="livesearchbtn btn btn-success btn-round btn-block heibtn" name="submitSearch" value="submitSearch"><i class="material-icons">search</i>Search</button></div></form><!--<form id="new_article_form" autocomplete="off" action="/action_page.php"><div class="autocomplete" style="width:300px;"><input id="myInput" type="text" name="myCountry" placeholder="Enter your search term here..."></div><input type="submit"></form> --></div><!--./card--></div><!--./col-md-6--></div><!--./row--></div><!--./container--></div><!--./page-header--><div class="main main-school"><section class="section section-basic"><div class="container"><div class="row"><div class="col-md-12"><div class="section-header spaceb20"><h1 class="head-title wow fadeInRight animated"><i class="fa fa-folder"></i> Reports</h1><div class='breadcrumb2'><a href="https://smart-hospital.in/"><i class="fa fa-home"></i></a><a href="https://smart-hospital.in/articles">Articles</a></div></div><!--./section-header--></div><!--./col-md-8--><div class="col-md-9 col-lg-9 col-sm-9"><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/daily-transaction-report"><i class="fa fa-book"></i> Daily Transaction Report</a></h4><div>To check User Log report, go to&nbsp;Reports &gt; Daily Transaction Report. Here select the Date From and Date To&nbsp;then click on&nbsp;Search button. Here you can see Daily Transaction details like Date, Total Transaction, Online, Offline, Amount and Collection List.&nbsp;Also you can download this report as PDF.To view each Daily Transaction individually click on View Collection button present at Action column. Here you can see Daily Transaction details Like Transaction ID, Date, Payment</div><a href="https://smart-hospital.in/article/daily-transaction-report" class="" title="Daily Transaction Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/all-transaction-report"><i class="fa fa-book"></i> All Transaction Report.</a></h4><div>To check Transaction report of patient, go to&nbsp;Reports &gt; All Transaction Report. Here select the&nbsp;Time Duration, Collected By&nbsp;and&nbsp;Select Head (optional) and then click on&nbsp;Search&nbsp;button. Here you can see patients transaction details for different treatments like for OPD, IPD, Pharmacy Bill, Pathology Test, Radiology Test, Blood Issue, Ambulance Call, Income. Expenses, Payroll Report.</div><a href="https://smart-hospital.in/article/all-transaction-report" class="" title="All Transaction Report.">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/appointment-report"><i class="fa fa-book"></i> Appointment Report</a></h4><div>To check patient&rsquo;s Appointment report, go to&nbsp;Reports &gt; Appointment Report. Here select the&nbsp;Time Duration, Doctor, Shift, Appointment Priority, and Source then click on&nbsp;Search&nbsp;button. Here you can see patient&rsquo;s Appointments details like&nbsp;Patient Name, Date, Phone, Gender, Doctor, Source, Appointment Details, Fees and Status&nbsp;etc.</div><a href="https://smart-hospital.in/article/appointment-report" class="" title="Appointment Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/opd-report"><i class="fa fa-book"></i> OPD Report</a></h4><div>To check patient OPD report, go to&nbsp;Reports &gt; OPD Report. Here select the&nbsp;Time Duration, Doctor, From Age, To Age, Gender, Symptoms&nbsp; and Findings then click on&nbsp;Search&nbsp;button. Here you can see patient OPD details like&nbsp;OPD No, Checkup ID, Patient Name, Doctor Name, Symptoms, Findings&nbsp;etc.</div><a href="https://smart-hospital.in/article/opd-report" class="" title="OPD Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/ipd-report"><i class="fa fa-book"></i> IPD Report</a></h4><div>To check patient IPD report, go to&nbsp;Reports &gt; IPD Report. Here select the Time Duration, Doctor, From Age, To Age, Gender, Symptoms&nbsp; and Findings then click on&nbsp;Search&nbsp;button. Here you can see patient IPD details like&nbsp;IPD No, Patient Name, Age, Gender, Mobile Number, Guardian Name, Doctor Name, Symptoms, Findings&nbsp;etc.</div><a href="https://smart-hospital.in/article/ipd-report" class="" title="IPD Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/opd-balance-report"><i class="fa fa-book"></i> OPD Balance Report</a></h4><div>To check patient OPD Balance report, go to&nbsp;Reports &gt; OPD Balance Report. Here select the&nbsp;Time Duration, From Age, To Age, Gender and Discharged then click on&nbsp;Search&nbsp;button. Here you can see patient&rsquo;s OPD Balance details like OPD No, Patient Name, Case ID, Age, Gender, Discharged Net Amount, Paid mount, Balance Amount etc.&nbsp;</div><a href="https://smart-hospital.in/article/opd-balance-report" class="" title="OPD Balance Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/ipd-balance-report"><i class="fa fa-book"></i> IPD Balance Report</a></h4><div>To check patient IPD Balance report, go to&nbsp;Reports &gt; IPD Balance Report. Here select the&nbsp;Time Duration, Patient Status, From Age, To Age, Gender then click on&nbsp;Search&nbsp;button. Here you can see patient&rsquo;s IPD Balance Amount details like IPD No, Case ID, Patient Name, Age, Gender, Mobile Number, Guardian Name, Discharged, Patient Active, Net Amount, Paid Amount, and Balance Amount etc.&nbsp;</div><a href="https://smart-hospital.in/article/ipd-balance-report" class="" title="IPD Balance Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/opd-discharge-patient-report"><i class="fa fa-book"></i> OPD Discharge Patient Report</a></h4><div>To check OPD Discharge Patient Report, go to Reports &gt; OPD Discharge Patient Report. Here select the&nbsp;Time Duration, Doctor, From Age, To Age, Gender, Discharge Status&nbsp;then click on&nbsp;Search button. Here you can see OPD Discharge Patient details like Patient Name, OPD No, Case ID, Gendar, Phone, Consultant, Appointment Date, Discharge Date, Discharge Status, Total Admit Days etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/opd-discharge-patient-report" class="" title="OPD Discharge Patient Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/ipd-discharge-patient-report"><i class="fa fa-book"></i> IPD Discharge Patient Report</a></h4><div>To check IPD Discharged Patient Report, go to Reports &gt; IPD&gt; IPD Discharged PatientHere select the Time Duration, Doctor, From Age, To Age, Gender, Discharge Status&nbsp;then click on&nbsp;Search button. Here you can see IPD Discharge Patient details like Patient Name, IPD No, Case ID, Gendar, Phone, Consultant, Bed, Admission Date, Discharge Date, Discharge Status, Total Admit Days etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/ipd-discharge-patient-report" class="" title="IPD Discharge Patient Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/pharmacy-bill-report"><i class="fa fa-book"></i> Pharmacy Bill Report</a></h4><div>To check Pharmacy Bill report, go to&nbsp;Reports &gt; Pharmacy &gt; Pharmacy Bill ReportHere select the Time Duration, Collected By, From Age, To Age, Gender and Payment Mode then click on&nbsp;Search&nbsp;button. Here you can see patient Pharmacy Bill details like Bill No, Patient Name, Age, Gender, Prescription No, Doctor Name, Collected By, Total Paid Amount etc.</div><a href="https://smart-hospital.in/article/pharmacy-bill-report" class="" title="Pharmacy Bill Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/expiry-medicine-report"><i class="fa fa-book"></i> Expiry Medicine Report</a></h4><div>To check Expiry Medicine report, go to&nbsp;Reports &gt; Expiry Medicine Report. Here select Time Duration, Medicine Category, Supplier then click on Search button. Here you can see Expiry Medicine Report details like Medicine Name, Batch No, Company Name, Medicine Category, Medicine Group, Supplier, Expire Date and Qty (Quantity). Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/expiry-medicine-report" class="" title="Expiry Medicine Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/pathology-patient-report"><i class="fa fa-book"></i> Pathology Patient Report</a></h4><div>To check Pathology Patient report, go to&nbsp;Reports &gt; Pathology Patient Report. Here select the&nbsp;Time Duration, Sample Collected Person Name, Category Name and Test Name then click on&nbsp;Search&nbsp;button. Here you can see patient pathology details like&nbsp;Bill No, Patient Name, Test Name, Consultant Doctor, Sample Collected Person Name, Amount, Paid Amount, Balance Amount&nbsp;etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/pathology-patient-report" class="" title="Pathology Patient Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/radiology-patient-report"><i class="fa fa-book"></i> Radiology Patient Report</a></h4><div>To check Radiology Patient report, go to&nbsp;Reports &gt; Radiology Patient Report. Here select the&nbsp;Time Duration, Sample Collected Person Name Category Name, and Test Name then click on&nbsp;Search&nbsp;button. Here you can see patient radiology details like&nbsp;Bill No, Patient Name, Category Name, Test Name, Consultant Doctor, Sample Collected Person Name, Amount, Paid Amount, Balance Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/radiology-patient-report" class="" title="Radiology Patient Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/ot-report"><i class="fa fa-book"></i> OT Report</a></h4><div>To check User Log report, go to&nbsp;Reports &gt; OT Report. Here select the&nbsp;Time Duration&nbsp;Consultant Doctor, Operation Category and Operation Name&nbsp;then click on&nbsp;Search button. Here you can see Patient OT details like Date, Reference No, OPD No, IPD No, Consultant Number,Operation Name, Operation Category, Result etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/ot-report" class="" title="OT Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/blood-issue-report"><i class="fa fa-book"></i> Blood Issue Report</a></h4><div>To check Blood Issue Report, go to&nbsp;Reports &gt; Blood Issue Report. Here select the&nbsp;Time Duration, Blood Collected By, Amount Collected By Blood Group and Blood Donor then click on&nbsp;Search&nbsp;button. Here you can see Blood Issue Report details like&nbsp;Bill No, Issue Date, Received To, Blood Group, Donor Name, Blood Collected By, Amount Collected By, Amount, Paid Amount, Balance Amount&nbsp;etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/blood-issue-report" class="" title="Blood Issue Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/component-issue-report"><i class="fa fa-book"></i> Component Issue Report</a></h4><div>To check Component Issue report, go to&nbsp;Reports &gt; Component Issue Report. Here select the&nbsp;Time Duration, Component Collected By, Amount Collected By, Blood Group and Component then click on&nbsp;Search&nbsp;button. Here you can see Component details like&nbsp;Bill No, Issue Date, Received To, Component, Bags, Amount, Paid Amount and Balance Amount&nbsp;etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/component-issue-report" class="" title="Component Issue Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/blood-donor-report"><i class="fa fa-book"></i> Blood Donor Report</a></h4><div>To check Component Issue report, go to&nbsp;Reports &gt; Blood DonorReport. Here select the&nbsp;Time Duration, Blood Group and Blood Donor then click on&nbsp;Search&nbsp;button. Here you can see Blood Donor details like&nbsp;Blood Group, Bags, Donor Name, Age, Donate Date, Apply Charges, Discount Percentage, Tax Percentage, Amount, Paid Amount&nbsp;etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/blood-donor-report" class="" title="Blood Donor Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/live-consultation-report"><i class="fa fa-book"></i> Live Consultation Report</a></h4><div>To check Live Consultation report, go to&nbsp;Reports &gt; Live Consultation Report. Here select the&nbsp;Time Duration, Created By and OPD / IPD then click on Search button. Here you can see Live Consultation details like&nbsp;Consultation Title, Patient, Date, Api Used, Create By and Total Join. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/live-consultation-report" class="" title="Live Consultation Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/live-meeting-report"><i class="fa fa-book"></i> Live Meeting Report</a></h4><div>To check Live Meeting report, go to&nbsp;Reports &gt; Live Meeting Report. Here select the&nbsp;Time Duration, Created By then click on&nbsp;Search&nbsp;button. Here you can see Live Meeting details like&nbsp;Meeting Title, Date, Api Used, Create By and Total Join. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/live-meeting-report" class="" title="Live Meeting Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/tpa-report"><i class="fa fa-book"></i> TPA Report</a></h4><div>To check TPA report, go to&nbsp;Reports &gt; TPA Report. Here select the&nbsp;Time Duration, Doctor, TPA, Case ID, Charge Category and Charge then click on&nbsp;Search&nbsp;button. Here you can see TPA details like&nbsp;Checkup/IPD No, Case ID, Head, TPA Name, Patient Name, Appointment Date, Doctor, Charge Name, Charge Category, Charge Type, Standard Charge, Applied Charge, TPA Charge, Tax Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/tpa-report" class="" title="TPA Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/income-report"><i class="fa fa-book"></i> Income Report</a></h4><div>To check Income report, go to&nbsp;Reports &gt; Income Report. Here select the&nbsp;Time Duration then click on&nbsp;Search&nbsp;button. Here you can see Income details like&nbsp;Name, Invoice Number, Income Head, Date, Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/income-report" class="" title="Income Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/income-group-report"><i class="fa fa-book"></i> Income Group Report</a></h4><div>To check Income Group report, go to&nbsp;Reports &gt; Income Group Report. Here select the&nbsp;Time Duration and Search Income Head then click on&nbsp;Search&nbsp;button. Here you can see Income Group details like&nbsp;Income Head, Income Id, Name, Date, Invoice Number, Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/income-group-report" class="" title="Income Group Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/expense-report"><i class="fa fa-book"></i> Expense Report</a></h4><div>To check Expense report, go to&nbsp;Reports &gt; Expense Report. Here select the&nbsp;Time Duration then click on&nbsp;Search&nbsp;button. Here you can see Expense details like&nbsp;Name, Invoice Number, Expense Head, Date, Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/expense-report" class="" title="Expense Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/expense-group-report"><i class="fa fa-book"></i> Expense Group Report</a></h4><div>To check Expense Group report, go to&nbsp;Reports &gt; Expense Group Report. Here select the&nbsp;Time Duration and Search Expense Head then click on&nbsp;Search&nbsp;button. Here you can see Expense Group details like&nbsp;Expense Head, Expense Id, Name, Date, Invoice Number, Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/expense-group-report" class="" title="Expense Group Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/ambulance-call-report"><i class="fa fa-book"></i> Ambulance Call Report.</a></h4><div>To check Ambulance Call report, go to&nbsp;Reports &gt; Ambulance Call Report. Here select the&nbsp;Time Duration, Collected By and Vehicle Model then click on&nbsp;Search&nbsp;button. Here you can see Ambulance Call details like&nbsp;Ambulance Call No, Patient Name, Date, Contact Number, Vehicle Number, Vehicle Model, Driver Name, Address, Ambulance, Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/ambulance-call-report" class="" title="Ambulance Call Report.">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/birth-report"><i class="fa fa-book"></i> Birth Report</a></h4><div>To check Birth report, go to&nbsp;Reports &gt; Birth Report. Here select the&nbsp;Time Duration and Gender then click on&nbsp;Search&nbsp;button. Here you can see Birth details like&nbsp;Reference Number, Case ID, Child Name, Gender, Birth Date, Weight, Mother Name, Father Name, Report etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/birth-report" class="" title="Birth Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/death-report"><i class="fa fa-book"></i> Death Report</a></h4><div>To check Death report, go to&nbsp;Reports &gt; Death Report. Here select the Time Duration and Gender then click on&nbsp;Search&nbsp;button. Here you can see Death details like&nbsp;Reference Number, Case Id, Guardian Name, Patient Name, Gender, Death Date, Report etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/death-report" class="" title="Death Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/payroll-month-report"><i class="fa fa-book"></i> Payroll Month Report</a></h4><div>To check Payroll Month report, go to&nbsp;Reports &gt; Payroll Month Report. Here select the&nbsp;Role, Month and Year then click on&nbsp;Search&nbsp;button. Here you can see Death details like&nbsp;Name, Role, Designation, Month &ndash;Year, Payslip, Basic Salary, Earning, Deduction, Gross Salary, Tax, Net Salary etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/payroll-month-report" class="" title="Payroll Month Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/payroll-report"><i class="fa fa-book"></i> Payroll Report</a></h4><div>To check Payroll report, go to&nbsp;Reports &gt; Payroll Report. Here select the&nbsp;Time Duration then click on&nbsp;Search&nbsp;button. Here you can see Payroll details like&nbsp;Name, Role, Designation, Month, Year, Payment Date, Payslip, Basic Salary, earning, Deduction, Gross Salary, Tax and Net Salary etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/payroll-report" class="" title="Payroll Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/staff-attendance-report"><i class="fa fa-book"></i> Staff Attendance Report</a></h4><div>To check Staff Attendance report, go to&nbsp;Reports &gt; Staff Attendance Report. Here select the Role, Month and Year then click on&nbsp;Search&nbsp;button. Here you can see attendance details for staff. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/staff-attendance-report" class="" title="Staff Attendance Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/user-log-report"><i class="fa fa-book"></i> User Log Report</a></h4><div>To check User Log report, go to&nbsp;Reports &gt; User Log Report. Here select the Time Duration&nbsp;and User Role then click on&nbsp;Search&nbsp;button. Here you can see User Log details like&nbsp;User, Role, IP Address, Login Time and User Agent etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/user-log-report" class="" title="User Log Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/patient-login-credential-report"><i class="fa fa-book"></i> Patient Login Credential Report</a></h4><div>To check Patient Login Credential report, go to&nbsp;Reports &gt; Patient, here you will see two report available Patient Visit Report and Patient Login Credential&nbsp;Now click on Patient Login Credential, in below Time Duration drop down will be open, using this drop down you can search and get the details of that time durationhere we select the Last Month and then click on Search, the details will be appear in below.Patient Login Credential,&nbsp;Here you can see Patient Login Credential</div><a href="https://smart-hospital.in/article/patient-login-credential-report" class="" title="Patient Login Credential Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/email-sms-log-report"><i class="fa fa-book"></i> Email / SMS Log Report</a></h4><div>To check Email/SMS Log report, go to&nbsp;Reports &gt; Email / SMS Log Report. Here select the&nbsp;Time Duration then click on&nbsp;Search&nbsp;button. Here you can see Email/SMS Log details like&nbsp;Title, Date, Email, SMS, Group, Individual etc. Also you can download this report as PDF.&nbsp;</div><a href="https://smart-hospital.in/article/email-sms-log-report" class="" title="Email / SMS Log Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/inventory-stock-report"><i class="fa fa-book"></i> Inventory Stock Report</a></h4><div>To check Inventory Stock report, go to&nbsp;Reports &gt; Inventory Stock Report. Here you can see Inventory Stock details like&nbsp;Name, Category, Supplier, Store, Total Quantity, and Available Quantity etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/inventory-stock-report" class="" title="Inventory Stock Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/inventory-item-report"><i class="fa fa-book"></i> Inventory Item report</a></h4><div>To check Inventory Item report, go to&nbsp;Reports &gt; Inventory &gt; Inventory Item Report.Now click on Inventory Item Report, below Time Duration drop down will appearHere select the Time Duration then click on&nbsp;Search button and you can see Inventory Item details like&nbsp;Name, Category, Supplier, Store, Quantity, Purchase Price, Date etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/inventory-item-report" class="" title="Inventory Item report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/inventory-issue-report"><i class="fa fa-book"></i> Inventory Issue Report</a></h4><div>To check Inventory Issue report, go to&nbsp;Reports &gt; Inventory Issue Report. Here select the&nbsp;Time Duration then click on&nbsp;Search&nbsp;button. Here you can see Inventory issue details like&nbsp;Item, Item Category, Issue - Return, Issue To, Issued By, Quantity etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/inventory-issue-report" class="" title="Inventory Issue Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/audit-trail-report"><i class="fa fa-book"></i> Audit Trail Report</a></h4><div>To check Audit Trail report, go to&nbsp;Reports &gt; Log &gt; Audit Trail Report.&nbsp;Now click on Audit Trail Report, Time Duration drop down will be displayHere, select the time duration for which you want to see the Audit Trail report&nbsp; then click on Search button, all details will be generated and display below. you can see Inventory Item details like&nbsp;Message, Users, IP Address, Action, Platform, Agent, Date - Time etc. Also download this report as PDF.To delete all audit trail</div><a href="https://smart-hospital.in/article/audit-trail-report" class="" title="Audit Trail Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/patient-visit-report"><i class="fa fa-book"></i> Patient Visit Report</a></h4><div>To check Patient Visit Report, go to&nbsp;Reports &gt; Patient Visit Report. Here enter the Patient Id (you will get patient id from OPD or IPD) then click on&nbsp;Search&nbsp;button. Here you can see Patient Visit details like&nbsp;OPD, IPD, Pathology, Radiology etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/patient-visit-report" class="" title="Patient Visit Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/patient-bill-report"><i class="fa fa-book"></i> Patient Bill Report</a></h4><div>To check Patient Bill Report, go to&nbsp;Reports &gt; Patient Bill Report. Here enter the Case ID (you will get Case ID from OPD or IPD) then click on&nbsp;Search&nbsp;button. Here you can see Patient Bill details like&nbsp;Patient Name, Module, OPD No, IPD No, Bill No, Payment Mode, Payment Date, Payment Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/patient-bill-report" class="" title="Patient Bill Report">Read More</a></div><div><h4 class="info-title"><a rel="bookmark" href="https://smart-hospital.in/article/referral-report"><i class="fa fa-book"></i> Referral Report</a></h4><div>To check Referral Report, go to&nbsp;Reports &gt; Finance &gt; Referral Report.Here search for Date, Payee, Patient Type and Patient (optional) then click on&nbsp;Search&nbsp;button. Here you can see Referral details like&nbsp;Name, Patient Name, Date with Time, Bill No, Bill Amount, Commission Percentage, Commission Amount etc. Also you can download this report as PDF.</div><a href="https://smart-hospital.in/article/referral-report" class="" title="Referral Report">Read More</a></div><!-- //===========pagination======== --><div class="pagination pull-right"></div><!-- ad bottom side --><div class="ad_bottom"></div></div><!--./col-md-9--><div class="col-md-3 col-lg-3 col-sm-3"><div class="ad_rsidebar"></div><div class="sidecard"><div class="content"><h4 class="info-title">Categories</h4><form method="GET" action="https://smart-hospital.in/home/<USER>" class="category_form"><select class="selectpicker category_dropdown" name="cat" style="width: 100%; display: block;"><option>Select Category</option><option value="1">Getting Started</option><option value="3">System Update</option><option value="4">Common Issues & Troubleshooting</option><option value="5">FAQ</option><option value="6">Miscellaneous</option><option value="7">Setup & Configuration</option><option value="8">System Settings</option><option value="9">Patient Panel</option><option value="10">Changelog</option><option value="11">Smart Hospital</option><option value="12">Smart Hospital Android App</option><option value="13">Reports</option><option value="15">Finance</option><option value="16">Income</option><option value="18">Front Office</option><option value="19">Appointment</option><option value="20">OPD - Out Patient</option><option value="21">IPD - In Patient</option><option value="22">Billing</option><option value="23">Pharmacy</option><option value="24">Pathology</option><option value="25">Radiology</option><option value="26">Blood Bank</option><option value="27">Live Consultation</option><option value="28">TPA Management</option><option value="29">Referral</option><option value="30">Certificate</option><option value="31">Ambulance</option><option value="32">Birth & Death Record</option><option value="33">Human Resource</option><option value="34">Messaging</option><option value="35">Download Center</option><option value="36">Inventory</option><option value="37">Front CMS</option><option value="41">Installation</option><option value="42">Duty Roster</option><option value="43">Annual Calendar</option><option value="44">Two Factor Authentication</option><option value="45">Multi Branch</option><option value="46">QR Code Attendance</option></select></form></div><!--./content--></div><!--./card--><div class="sidecard"><div class="content"><h4 class="info-title">Latest Articles</h4><ul class="side-list"><li><a href="https://smart-hospital.in/article/how-to-disable-the-auto-attendance-and-mark-the-attendance-of-hospital-staff-using-qr-code-barcode">How to disable the auto attendance and mark the attendance of hospital staff using QR Code / Barcode?</a></li><li><a href="https://smart-hospital.in/article/about-qr-code-attendance">About QR Code Attendance</a></li><li><a href="https://smart-hospital.in/article/want-to-know-about-smart-hospital-biometric-attendance-app">Want to know about Smart Hospital Biometric Attendance App?</a></li><li><a href="https://smart-hospital.in/article/version-5-0">Version 5.0</a></li><li><a href="https://smart-hospital.in/article/how-to-show-annual-calendar-tab-on-front-site-of-smart-hospital">How to show annual calendar tab on front site of smart hospital?</a></li><li><a href="https://smart-hospital.in/article/how-to-add-vacation-in-annual-calendar">How to add vacation in annual calendar?</a></li><li><a href="https://smart-hospital.in/article/how-to-add-activity-in-annual-calendar">How to add activity in annual calendar?</a></li><li><a href="https://smart-hospital.in/article/how-to-add-holiday-in-annual-calendar-on-smart-hospital">How to add holiday in annual calendar on smart hospital?</a></li><li><a href="https://smart-hospital.in/article/how-to-assign-the-roster-to-the-staff">How to assign the roster to the staff?</a></li><li><a href="https://smart-hospital.in/article/how-to-add-the-roster-in-duty-roster-module">How to add the roster in duty roster module?</a></li></ul></div><!--./content--></div><!--./card--></div><!--./col-md-3--></div><!--./row--></div><!--./container--></section></div><!--./main-school--><footer class="full-footer"><div class="container"><div class="row"><div class="col-md-12 fullfooter"><div class="col-lg-3 col-md-3 col-sm-6"><h3 class="fo-title">Meet Smart Hospital</h3><div class="divider"></div><ul class="fo-list"><li><a href="https://smart-hospital.in/">Home</a></li><li><a href="https://smart-hospital.in/feature">Features</a></li><li><a href="https://smart-hospital.in/demo">Demo</a></li><li><a href="https://smart-hospital.in/how-it-work">How it Works</a></li><li><a href="https://smart-school.in/docs" target="_blank">Documentation</a></li><li><a href="https://smart-hospital.in/articles">Articles</a></li><li class="" ><a href="https://smart-hospital.in/faq">FAQ</a></li><li><a href="https://support.qdocs.net" target="_blank">Support</a></li></ul></div><!--./col-md-3--><div class="col-lg-7 col-md-6 col-sm-6"><h3 class="fo-title">Testimonials</h3><div class="divider"></div><div class="col-md-12 col-sm-12"><div id="carousel-testimonial" class="carousel slide carousel2" data-ride="carousel"><div class="carousel-inner roundlr" role="listbox"><div class="item active"><a href="https://codecanyon.net/item/smart-hospital-hospital-management-system/reviews/23205038" target="_blank"><div class="card card-testimonial card-plain"><div class="footer testi-left"><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i></div><h4 class="card-title testi-title">for Feature Availability</h4><div class="clear"><h5 class="card-description card-title2">The one stop solution for hospital managements, also well documented. Easy and user friendly navigation & interfaces.Also a less bug and more responsive friendly.</h5><h6 class="category2">by GanpathTech</h6></div></div></a></div><!--./item1--><div class="item"><a href="https://codecanyon.net/item/smart-hospital-hospital-management-system/reviews/23205038" target="_blank"><div class="card card-testimonial card-plain"><div class="footer testi-left"><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i></div><h4 class="card-title testi-title">for Customer Support</h4><div class="clear"><h5 class="card-description card-title2">Amazing Support and Application for Hospital</h5><h6 class="category2">by hassaan051</h6></div></div></a></div><!--./item2--><div class="item"><a href="https://codecanyon.net/item/smart-hospital-hospital-management-system/reviews/23205038" target="_blank"><div class="card card-testimonial card-plain"><div class="footer testi-left"><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i></div><h4 class="card-title testi-title">for Code Quality</h4><div class="clear"><h5 class="card-description card-title2">It's perfect System for Hospital management My appreciate to developers</h5><h6 class="category2">by Matrixagha</h6></div></div></a></div><!--./item3--><div class="item"><a href="https://codecanyon.net/item/smart-hospital-hospital-management-system/reviews/23205038" target="_blank"><div class="card card-testimonial card-plain"><div class="footer testi-left"><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i></div><h4 class="card-title testi-title">for Customer Support</h4><div class="clear"><h5 class="card-description card-title2">Excellent customer support and design quality.</h5><h6 class="category2">by Hemadrit</h6></div></div></a></div><!--./item4--><div class="item"><a href="https://codecanyon.net/item/smart-hospital-hospital-management-system/reviews/23205038" target="_blank"><div class="card card-testimonial card-plain"><div class="footer testi-left"><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i><i class="material-icons text-warning">star</i></div><h4 class="card-title testi-title">for Feature Availability</h4><div class="clear"><h5 class="card-description card-title2">Qdocs simply rock.... their products are awsum with clean code and good features with good support (Though I i Believe they should decrease Response time to 12 Hrs from current 24 Hrs ) but due to good sales i guess it shall be hard for them</h5><h6 class="category2">by justgroup</h6></div></div></a></div><!--./item4--></div><!--./carousel-inner--><a class="left carousel-control" href="#carousel-testimonial" role="button" data-slide="prev"><i class="material-icons" aria-hidden="true">chevron_left</i></a><a class="right carousel-control" href="#carousel-testimonial" role="button" data-slide="next"><i class="material-icons" aria-hidden="true">chevron_right</i></a></div><!--./carousel-testimonial--></div><!--./col-md-12--></div><!--./col-md-5--><div class="col-lg-2 col-md-3 col-sm-3"><h3 class="fo-title">ALSO AVAILABLE ON</h3><div class="divider"></div><a href="https://play.google.com/store/apps/details?id=com.pss.smartschool" target="_blank"><img src="https://smart-school.in/assets/admin/assets/img/google-play.png" width="217" height="71" class="img-responsive"></a></div><div><!--./col-md-12--></div><!--./row--></div><!--./container--></footer><section class="copy-right2"><div class="container"><div class="row"><div class="col-lg-6 col-md-6 col-sm-6"><p class="fleft"><a href="http://qdocs.in" target="_blank"><img src="https://smart-hospital.in/assets/admin/assets/img/qd-new.png" width="100" alt="Smart School"style="padding-right: 10px;vertical-align: text-bottom;" /></a> © 2024 All Rights Reserved</p><a class="w-toplink active page-scrollimg-raised" style="display: none;" href="#Home" id="back-to-top"><i class="fa fa-angle-up" aria-hidden="true"></i></a></div><div class="col-lg-6 col-md-6 col-sm-6"><ul class="footer-social fright"><li><a href="https://www.facebook.com/Smart-School-1471650676181486/" target="_blank" class="facebook"><i class="fa fa-facebook"></i></a></li><li><a href="https://twitter.com/SmartSchoolLive" class="twitter" target="_blank"><i class="fa fa-twitter"></i></a></li><li><a href="https://www.youtube.com/playlist?list=PLRyqKhLrbTa04IOguu-mjojT5sFbInUQa" class="youtube" target="_blank"><i class="fa fa-youtube"></i></a></li></ul></div><!--./col-md-3--></div><!--./row--></div><!--./container--></section></div><!--./wrapper--><!--Core JS Files--><script src="https://smart-hospital.in/assets/admin/assets/js/jquery.min.js" type="text/javascript"></script><script src="https://smart-hospital.in/assets/admin/assets/js/bootstrap.min.js" type="text/javascript"></script><script src="https://smart-hospital.in/assets/admin/assets/js/scrolling-nav.js"></script><script src="https://smart-hospital.in/assets/admin/assets/js/support-qdocs.js" type="text/javascript"></script><script src="https://smart-hospital.in/assets/admin/assets/js/owl.carousel.min.js" type="text/javascript"></script><script type="text/javascript">
         // course carousel (uses the Owl Carousel library)
          $(".banner-carousel").owlCarousel({
            autoplay: true,
            nav    : true,
            autoplayTimeout:12000,
            transitionStyle : "fade",
            //animateIn: 'bounceInUp',
            dots: false,
            loop: true,
            navText : ["<i class='fa fa-angle-left'></i>","<i class='fa fa-angle-right'></i>"],
            responsive: { 0: { items: 1 }, 768: { items: 1 }, 900: { items: 1 }
            }
          });
       </script><script src="https://smart-hospital.in/assets/admin/assets/js/qdocs-kite.js" type="text/javascript"></script><script src="https://smart-hospital.in/assets/admin/assets/js/jquery.easing.min.js"></script><!-- <script src=""></script> --><script type="text/javascript">
            $(document).ready(function () {
                $("ul.sub-menu").parent().addClass("dropdown");
                $("ul.sub-menu").addClass("dropdown-menu");
                $("ul#main-menu li.dropdown a").addClass("dropdown-toggle");
                $("ul.sub-menu li a").removeClass("dropdown-toggle");
                $('.navbar .dropdown-toggle').append('<b class="caret"></b>');
                $('a.dropdown-toggle').attr('data-toggle', 'dropdown');
            });
            // $('.wodryRX').wodry({
            //     animation: 'rotateX',
            //     delay: 4000,
            //     animationDuration: 1600
            // });

            $('.faq_question').click(function () {
                //$(this).next('ac-small').slideToggle('500');
                //$(".ac-container label").slideDown();
                $(this).find('i').toggleClass('fa-plus fa-minus')

            });
            $(document).ready(function () {
                $(".ac-container label").click(function () {
                    $(".ac-small").slideUp(150).attr('aria-hidden', 'true');
                });
                $(".ac-container").click(function () {
                    $(".ac-small").slideDown(150).attr('aria-hidden', 'false');
                });
            });


        </script><script type="text/javascript">
            function toggleIcon(e) {
                $(e.target)
                        .prev('.panel-heading')
                        .find(".more-less")
                        .toggleClass('fa-plus-circle fa-minus-circle');
            }
            $('.panel-group').on('hidden.bs.collapse', toggleIcon);
            $('.panel-group').on('shown.bs.collapse', toggleIcon);

        </script><!-- <script src=""></script><script type="text/javascript">
              $('.main-div').enscroll({
          showOnHover: false,
          verticalTrackClass: 'track3',
          verticalHandleClass: 'handle3'
          });
          </script>--><!-- Latest compiled and minified JavaScript --><script src="https://smart-hospital.in/assets/admin/assets/js/bootstrap-select.min.js"></script><script src="https://smart-hospital.in/assets/admin/assets/js/wow.min.js"></script><script type="text/javascript">
            $('select').selectpicker();

            wow = new WOW({}).init();
        </script></body></html><script type="text/javascript">
            var currentFocus;
            $(document).ready(function () {
                $(document).on('propertychange input', '#myInput', function () {
                    $('#myInput').popover('hide');
                    currentFocus = -1;
                    var keyword_value = $(this).val();
                    if (!keyword_value.length) {
                        $('.autocomplete-items').remove();
                        return false;
                    }
                    $.ajax({
                        url: "https://smart-hospital.in/home/<USER>",
                        data: {'keyword': keyword_value},
                        type: 'POST',
                        dataType: 'JSON',
                        beforeSend: function () {


                        },
                        success: function (result) {
                            $('.autocomplete-items').remove();
                            var div = $('<div />', {
                                "class": 'autocomplete-items',
                                "id": 'myInputautocomplete-list',
                            }
                            );
                            $.each(result.result, function (index, value) {

                                div.append($('<div>', {
                                    id: 'inner-div',
                                    class: (value.category_name == "FAQ") ? "FAQ" : "standard",
                                }).append($('<a>', {
                                    id: 'innerdiv',
                                    title: value.slug,
                                    text: value.title + " ",
                                    href: (value.category_name == "FAQ") ? base_url + "faq/" + value.slug : base_url + "article/" + value.slug
                                }).append($('<span>', {
                                    class: 'badge',
                                    text: value.category_name

                                }))).append($('<input>', {
                                    id: 'innerdiv',
                                    type: 'hidden',
                                    val: value.title

                                })));
                            });

                            div.appendTo($('form#new_article_form').find('div.autocomplete'));

                        },
                        error: function (xhr) { // if error occured
                            alert("Error occured.please try again");

                        },
                        complete: function () {

                        }

                    });

                });

                $(document).on('keydown', '#myInput', function (e) {
                    if (e.keyCode == 40) {
                        currentFocus++;

                        addActive();
                        scrollsearch();
                    } else if (e.keyCode == 38) { //up
                        currentFocus--;
                        addActive();
                        scrollsearch();
                    } else if (e.keyCode == 13) {
                        /*If the ENTER key is pressed, prevent the form from being submitted,*/
                        // e.preventDefault();


                        $('form#new_article_form').find('div.autocomplete-items div').eq(currentFocus).trigger("click");
                    } else if (e.keyCode == 27) {
                        $('.autocomplete-items').remove();
                    }

                });

            });
            function addActive() {
                /*a function to classify an item as "active":*/
                var div_list = $('form#new_article_form').find('div.autocomplete-items div');

                /*start by removing the "active" class on all items:*/
                removeActive();

                if (currentFocus >= div_list.length)
                    currentFocus = 0;
                // if (currentFocus < 0) currentFocus = (x.length - 1);
                /*add class "autocomplete-active":*/

                // x[currentFocus].classList.add("autocomplete-active");
                $('form#new_article_form').find('div.autocomplete-items div').eq(currentFocus).addClass("autocomplete-active");
            }

            function removeActive() {
                /*a function to remove the "active" class from all autocomplete items:*/
                $('form#new_article_form').find('div.autocomplete-items div').removeClass('autocomplete-active');
            }

            $(document).on('click', '.autocomplete-active', function () {
                $('#myInput').val("").val($('.autocomplete-active').find('input').val());
            });
            $(document).on('click', function (e) {
                closeArticleLists(e.target);
            });
            function closeArticleLists(elemnt) {

                if (elemnt != document.getElementById("myInput")) {
                    $('.autocomplete-items').remove();
                }
            }
            function scrollsearch() {
                $(".autocomplete-items").scrollTop(0);//set to top
                $(".autocomplete-items").scrollTop($('.autocomplete-active:first').offset().top - ($(".autocomplete-items").height() + ($(".autocomplete-active").height() * 4)));
            }
        </script><script type="text/javascript">

            $(function () {
                $("form#new_article_form").submit(function () {

                    var valid = 0;
                    var value = $.trim($(this).find('input[type=text]').val());

                    if (value.length >= 3) {
                        valid += 1;
                    }

                    if (valid) {
                        return true;
                    }
                    else {
                        $('#myInput').popover('show');
                        return false;
                    }
                });
            });


            $('#myInput').popover({
                html: true,
                trigger: 'manual',
                placement: 'bottom',
                content: '<div class="text text-danger">Please enter atleast 3 character</div>',
                template: '<div class="popover my-popover" role="tooltip"><div class="arrow"></div><div class="popover-content"></div></div>'
            });


            $(document).on('change', '.category_dropdown', function () {
                this.form.submit();
            });
        </script>