import requests
from bs4 import BeautifulSoup
import pandas as pd
import pdfkit
import os

# URL of the report page
url = "https://smart-hospital.in/article/opd-discharge-patient-report"

# Create session
session = requests.Session()
response = session.get(url)

# Parse HTML
soup = BeautifulSoup(response.text, "html.parser")

# If the page has a form, extract it and mimic submission
# For this sample, we're assuming the report loads instantly
# Otherwise, you'd need to inspect the form and simulate POST

# Find the report table
table = soup.find("table")

if not table:
    print("No table found on the page. Try after submitting filters 
manually.")
    exit()

# Convert table to DataFrame
df = pd.read_html(str(table))[0]

# Save as CSV (optional)
df.to_csv("opd_report.csv", index=False)

# Convert to HTML string
html = df.to_html(index=False)

# Save as HTML
with open("opd_report.html", "w") as f:
    f.write(html)

# Convert HTML to PDF
pdfkit.from_file("opd_report.html", "opd_report.pdf")

print("✅ Report saved as PDF: opd_report.pdf")

