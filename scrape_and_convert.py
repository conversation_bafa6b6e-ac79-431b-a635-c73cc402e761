import requests
from bs4 import BeautifulSoup
import pandas as pd
import pdfkit
import os
from datetime import datetime

# URL of the report page
url = "https://smart-hospital.in/article/"

# Create session with headers to mimic a real browser
session = requests.Session()
session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
})

try:
    print(f"🔄 Fetching data from: {url}")
    response = session.get(url, timeout=30)
    response.raise_for_status()  # Raise an exception for bad status codes
    print(f"✅ Successfully fetched page (Status: {response.status_code})")
except requests.exceptions.RequestException as e:
    print(f"❌ Error fetching the page: {e}")
    exit(1)

# Parse HTML
print("🔄 Parsing HTML content...")
soup = BeautifulSoup(response.text, "html.parser")

# If the page has a form, extract it and mimic submission
# For this sample, we're assuming the report loads instantly
# Otherwise, you'd need to inspect the form and simulate POST

# Find the report table
print("🔍 Looking for tables on the page...")
tables = soup.find_all("table")

if not tables:
    print("❌ No tables found on the page.")
    print("💡 This might mean:")
    print("   - The page requires authentication")
    print("   - The data is loaded dynamically with JavaScript")
    print("   - The table structure has changed")
    print("   - Filters need to be submitted manually")

    # Save the HTML for debugging
    with open("debug_page.html", "w", encoding="utf-8") as f:
        f.write(response.text)
    print("🐛 Saved page HTML as 'debug_page.html' for inspection")
    exit(1)

print(f"✅ Found {len(tables)} table(s) on the page")

# Try to convert the first table to DataFrame
try:
    # Use the first table or look for the most relevant one
    table = tables[0]  # You might need to adjust this based on the page structure
    print("🔄 Converting table to DataFrame...")
    df = pd.read_html(str(table))[0]
    print(f"✅ Successfully extracted table with {len(df)} rows and {len(df.columns)} columns")
except Exception as e:
    print(f"❌ Error converting table to DataFrame: {e}")
    exit(1)

# Generate timestamp for file names
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
base_filename = f"opd_report_{timestamp}"

# Save as CSV
csv_filename = f"{base_filename}.csv"
print(f"💾 Saving as CSV: {csv_filename}")
df.to_csv(csv_filename, index=False)

# Convert to HTML string with better styling
html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>OPD Discharge Patient Report - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        h1 {{ color: #333; }}
    </style>
</head>
<body>
    <h1>OPD Discharge Patient Report</h1>
    <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    {df.to_html(index=False, classes='report-table')}
</body>
</html>
"""

# Save as HTML
html_filename = f"{base_filename}.html"
print(f"💾 Saving as HTML: {html_filename}")
with open(html_filename, "w", encoding="utf-8") as f:
    f.write(html_content)

# Convert HTML to PDF
try:
    pdf_filename = f"{base_filename}.pdf"
    print(f"📄 Converting to PDF: {pdf_filename}")
    pdfkit.from_file(html_filename, pdf_filename)
    print(f"✅ Report saved as PDF: {pdf_filename}")
except Exception as e:
    print(f"⚠️  Warning: Could not create PDF: {e}")
    print("💡 Make sure wkhtmltopdf is installed: https://wkhtmltopdf.org/downloads.html")

print(f"\n🎉 Scraping completed successfully!")
print(f"📁 Files created:")
print(f"   - CSV: {csv_filename}")
print(f"   - HTML: {html_filename}")
if 'pdf_filename' in locals():
    print(f"   - PDF: {pdf_filename}")

