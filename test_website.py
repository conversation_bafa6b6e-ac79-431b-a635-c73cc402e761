#!/usr/bin/env python3
"""
Simple script to test website accessibility and explore its structure
"""

import requests
from bs4 import BeautifulSoup
import sys

def test_url(url):
    """Test if a URL is accessible and show basic info"""
    print(f"🔍 Testing URL: {url}")
    
    # Create session with headers
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    try:
        response = session.get(url, timeout=30)
        print(f"✅ Status Code: {response.status_code}")
        print(f"📏 Content Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            # Parse HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find title
            title = soup.find('title')
            if title:
                print(f"📄 Page Title: {title.get_text().strip()}")
            
            # Find tables
            tables = soup.find_all('table')
            print(f"📊 Tables found: {len(tables)}")
            
            # Find forms
            forms = soup.find_all('form')
            print(f"📝 Forms found: {len(forms)}")
            
            # Show first few lines of content
            print("\n📖 First 500 characters of content:")
            print("-" * 50)
            print(response.text[:500])
            print("-" * 50)
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
        return False

if __name__ == "__main__":
    # Default URL or from command line
    default_url = "https://smart-hospital.in"
    
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = default_url
        print(f"💡 Using default URL. You can specify a different URL: python test_website.py <URL>")
    
    test_url(url)
